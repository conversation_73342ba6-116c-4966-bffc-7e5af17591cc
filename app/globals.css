@import "tailwindcss";

/* TailwindCSS v4 Theme Configuration - Financial Trading Platform */
@theme {
  /* Breakpoints */
  --breakpoint-xs: 375px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Font Families */
  --font-sans:
    "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: "JetBrains Mono", "Fira Code", Consolas, monospace;
  --font-display:
    "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

  /* Spacing Scale */
  --space-px: 1px;
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-none: 0;
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg:
    0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl:
    0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Financial Trading Colors - Light Mode */
  --color-background: hsl(0 0% 99%);
  --color-foreground: hsl(222 15% 15%);
  --color-surface: hsl(0 0% 100%);
  --color-surface-elevated: hsl(0 0% 98%);

  /* Card System */
  --color-card: hsl(0 0% 100%);
  --color-card-foreground: hsl(222 15% 15%);
  --color-card-border: hsl(220 13% 91%);

  /* Primary Brand Colors */
  --color-primary: hsl(222 47% 11%);
  --color-primary-foreground: hsl(0 0% 98%);
  --color-primary-hover: hsl(222 47% 15%);

  /* Secondary Colors */
  --color-secondary: hsl(220 14% 96%);
  --color-secondary-foreground: hsl(222 15% 15%);
  --color-secondary-hover: hsl(220 14% 93%);

  /* Muted Colors */
  --color-muted: hsl(220 14% 96%);
  --color-muted-foreground: hsl(220 9% 46%);

  /* Accent Colors */
  --color-accent: hsl(220 14% 96%);
  --color-accent-foreground: hsl(222 15% 15%);

  /* Financial Status Colors */
  --color-bullish: hsl(142 76% 36%);
  --color-bullish-foreground: hsl(0 0% 100%);
  --color-bullish-light: hsl(142 76% 96%);
  --color-bullish-border: hsl(142 76% 86%);

  --color-bearish: hsl(0 84% 60%);
  --color-bearish-foreground: hsl(0 0% 100%);
  --color-bearish-light: hsl(0 84% 96%);
  --color-bearish-border: hsl(0 84% 86%);

  --color-neutral: hsl(220 9% 46%);
  --color-neutral-foreground: hsl(0 0% 100%);
  --color-neutral-light: hsl(220 14% 96%);
  --color-neutral-border: hsl(220 13% 91%);

  /* Status Colors */
  --color-success: hsl(142 76% 36%);
  --color-success-foreground: hsl(0 0% 100%);
  --color-warning: hsl(38 92% 50%);
  --color-warning-foreground: hsl(0 0% 100%);
  --color-destructive: hsl(0 84% 60%);
  --color-destructive-foreground: hsl(0 0% 100%);
  --color-info: hsl(221 83% 53%);
  --color-info-foreground: hsl(0 0% 100%);

  /* Border Colors */
  --color-border: hsl(220 13% 91%);
  --color-border-strong: hsl(220 13% 81%);
  --color-input: hsl(220 13% 91%);
  --color-ring: hsl(222 47% 11%);

  /* Chart Colors */
  --color-chart-1: hsl(12 76% 61%);
  --color-chart-2: hsl(173 58% 39%);
  --color-chart-3: hsl(197 37% 24%);
  --color-chart-4: hsl(43 74% 66%);
  --color-chart-5: hsl(27 87% 67%);
  --color-chart-6: hsl(262 83% 58%);
  --color-chart-7: hsl(295 70% 60%);
  --color-chart-8: hsl(336 75% 40%);

  /* Base radius value */
  --radius: 0.5rem;
}

@theme dark {
  /* Financial Trading Colors - Dark Mode */
  --color-background: hsl(222 47% 4%);
  --color-foreground: hsl(220 9% 95%);
  --color-surface: hsl(222 47% 6%);
  --color-surface-elevated: hsl(222 47% 8%);

  /* Card System */
  --color-card: hsl(222 47% 6%);
  --color-card-foreground: hsl(220 9% 95%);
  --color-card-border: hsl(222 47% 12%);

  /* Primary Brand Colors */
  --color-primary: hsl(220 9% 95%);
  --color-primary-foreground: hsl(222 47% 11%);
  --color-primary-hover: hsl(220 9% 90%);

  /* Secondary Colors */
  --color-secondary: hsl(222 47% 8%);
  --color-secondary-foreground: hsl(220 9% 95%);
  --color-secondary-hover: hsl(222 47% 12%);

  /* Muted Colors */
  --color-muted: hsl(222 47% 8%);
  --color-muted-foreground: hsl(220 9% 65%);

  /* Accent Colors */
  --color-accent: hsl(222 47% 8%);
  --color-accent-foreground: hsl(220 9% 95%);

  /* Financial Status Colors - Dark Mode */
  --color-bullish: hsl(142 76% 45%);
  --color-bullish-foreground: hsl(222 47% 4%);
  --color-bullish-light: hsl(142 76% 8%);
  --color-bullish-border: hsl(142 76% 15%);

  --color-bearish: hsl(0 84% 65%);
  --color-bearish-foreground: hsl(222 47% 4%);
  --color-bearish-light: hsl(0 84% 8%);
  --color-bearish-border: hsl(0 84% 15%);

  --color-neutral: hsl(220 9% 65%);
  --color-neutral-foreground: hsl(222 47% 4%);
  --color-neutral-light: hsl(222 47% 8%);
  --color-neutral-border: hsl(222 47% 12%);

  /* Status Colors - Dark Mode */
  --color-success: hsl(142 76% 45%);
  --color-success-foreground: hsl(222 47% 4%);
  --color-warning: hsl(38 92% 55%);
  --color-warning-foreground: hsl(222 47% 4%);
  --color-destructive: hsl(0 84% 65%);
  --color-destructive-foreground: hsl(222 47% 4%);
  --color-info: hsl(221 83% 60%);
  --color-info-foreground: hsl(222 47% 4%);

  /* Border Colors - Dark Mode */
  --color-border: hsl(222 47% 12%);
  --color-border-strong: hsl(222 47% 18%);
  --color-input: hsl(222 47% 12%);
  --color-ring: hsl(220 9% 95%);

  /* Chart Colors - Dark Mode */
  --color-chart-1: hsl(12 76% 70%);
  --color-chart-2: hsl(173 58% 50%);
  --color-chart-3: hsl(197 37% 35%);
  --color-chart-4: hsl(43 74% 75%);
  --color-chart-5: hsl(27 87% 75%);
  --color-chart-6: hsl(262 83% 70%);
  --color-chart-7: hsl(295 70% 70%);
  --color-chart-8: hsl(336 75% 55%);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  /* Enhanced base styles for financial trading platform */
  * {
    border-color: var(--color-border);
  }

  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
    scroll-behavior: smooth;
  }

  body {
    margin: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: var(--font-sans);
    background-color: var(--color-background);
    color: var(--color-foreground);
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  main {
    flex: 1;
  }

  /* Typography improvements */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-display);
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }

  h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
  }

  h2 {
    font-size: var(--font-size-2xl);
  }

  h3 {
    font-size: var(--font-size-xl);
  }

  /* Financial data typography */
  .financial-number {
    font-family: var(--font-mono);
    font-variant-numeric: tabular-nums;
    letter-spacing: -0.025em;
  }

  /* Focus styles */
  :focus-visible {
    outline: 2px solid var(--color-ring);
    outline-offset: 2px;
  }

  /* Selection styles */
  ::selection {
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
  }
}

.text-gradient {
  background: linear-gradient(to right, #00dc82, #36e4da);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (prefers-color-scheme: dark) {
  .text-gradient {
    background: linear-gradient(to right, #00dc82, #36e4da);
  }
}

/* Custom scrollbar styles */
.scrollbar-custom::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: #888;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utilities for TailwindCSS v4 - Financial Trading Platform */
@utility text-gradient {
  background: linear-gradient(
    to right,
    var(--color-primary),
    var(--color-primary-foreground)
  );
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}

@utility safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Financial Status Utilities */
@utility status-bullish {
  background-color: var(--color-bullish-light);
  color: var(--color-bullish);
  border-color: var(--color-bullish-border);
}

@utility status-bearish {
  background-color: var(--color-bearish-light);
  color: var(--color-bearish);
  border-color: var(--color-bearish-border);
}

@utility status-neutral {
  background-color: var(--color-neutral-light);
  color: var(--color-neutral);
  border-color: var(--color-neutral-border);
}

/* Financial Number Formatting */
@utility financial-number {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums;
  letter-spacing: -0.025em;
  font-weight: 500;
}

/* Card Enhancements */
@utility card-elevated {
  background-color: var(--color-surface-elevated);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-lg);
}

@utility card-interactive {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.card-interactive:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Table Enhancements */
@utility table-row-hover {
  transition: background-color 0.15s ease-in-out;
}

@utility table-row-hover:hover {
  background-color: var(--color-muted);
}

/* Animation utilities for compatibility with tailwindcss-animate */
@utility animate-spin {
  animation: spin 1s linear infinite;
}

@utility animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@utility animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@utility animate-bounce {
  animation: bounce 1s infinite;
}

@utility animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@utility animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Enhanced Keyframes */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-primary-foreground bg-clip-text text-transparent;
  }
  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Enhanced Footer Navigation */
.footer-nav {
  position: sticky;
  bottom: 0;
  width: 100%;
  background-color: var(--color-surface);
  border-top: 1px solid var(--color-border);
  backdrop-filter: blur(8px);
  z-index: 50;
  box-shadow: var(--shadow-lg);
}

/* Professional scrollbar styling */
.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: var(--color-border-strong);
  border-radius: var(--radius-full);
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: var(--color-muted-foreground);
}

/* Financial data table enhancements */
.financial-table {
  font-variant-numeric: tabular-nums;
}

.financial-table th {
  font-weight: 600;
  color: var(--color-muted-foreground);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.financial-table td {
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-indicator.bullish {
  background-color: var(--color-bullish-light);
  color: var(--color-bullish);
  border: 1px solid var(--color-bullish-border);
}

.status-indicator.bearish {
  background-color: var(--color-bearish-light);
  color: var(--color-bearish);
  border: 1px solid var(--color-bearish-border);
}

.status-indicator.neutral {
  background-color: var(--color-neutral-light);
  color: var(--color-neutral);
  border: 1px solid var(--color-neutral-border);
}
